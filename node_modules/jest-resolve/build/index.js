'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
var _exportNames = {};
exports.default = void 0;
var _resolver = _interopRequireDefault(require('./resolver'));
var _utils = require('./utils');
Object.keys(_utils).forEach(function (key) {
  if (key === 'default' || key === '__esModule') return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _utils[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _utils[key];
    }
  });
});
function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var _default = _resolver.default;
exports.default = _default;
